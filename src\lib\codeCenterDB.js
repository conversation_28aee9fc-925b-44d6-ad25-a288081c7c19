import Dexie from 'dexie';

// 创建专门用于CodeCenter的数据库
class CodeCenterDB extends Dexie {
  constructor() {
    super('CodeCenterDB');
    
    // 定义数据库版本和表结构
    this.version(1).stores({
      components: '++id, name, description, category, usage, codeContent, imageUrl, createdAt, updatedAt'
    });

    this.components = this.table('components');
  }
}

// 创建数据库实例
const codeCenterDB = new CodeCenterDB();

// 导出数据库操作方法
export const componentDB = {
  // 添加组件
  async addComponent(component) {
    const cleanComponent = {
      name: component.name,
      description: component.description,
      category: component.category,
      usage: component.usage,
      codeContent: component.codeContent,
      imageUrl: component.imageUrl,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return await codeCenterDB.components.add(cleanComponent);
  },
  
  // 更新组件
  async updateComponent(id, component) {
    // 确保使用正确的ID进行更新
    const updateData = {
      name: component.name,
      description: component.description,
      category: component.category,
      usage: component.usage,
      codeContent: component.codeContent,
      imageUrl: component.imageUrl,
      updatedAt: new Date()
    };

    // 使用数字ID进行更新（Dexie自动生成的ID是数字类型）
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    return await codeCenterDB.components.update(numericId, updateData);
  },
  
  // 删除组件
  async deleteComponent(id) {
    // 确保使用正确的ID类型进行删除
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    return await codeCenterDB.components.delete(numericId);
  },
  
  // 获取所有组件
  async getAllComponents() {
    return await codeCenterDB.components.orderBy('updatedAt').reverse().toArray();
  },
  
  // 根据ID获取组件
  async getComponentById(id) {
    // 确保使用正确的ID类型进行查询
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    return await codeCenterDB.components.get(numericId);
  },
  
  // 搜索组件
  async searchComponents(keyword) {
    return await codeCenterDB.components
      .filter(component => 
        component.name.toLowerCase().includes(keyword.toLowerCase()) ||
        component.description.toLowerCase().includes(keyword.toLowerCase()) ||
        component.usage.toLowerCase().includes(keyword.toLowerCase())
      )
      .toArray();
  },
  
  // 根据分类获取组件
  async getComponentsByCategory(category) {
    if (category === '') {
      return await this.getAllComponents();
    }
    return await codeCenterDB.components
      .where('category')
      .equals(category)
      .toArray();
  }
};

// 默认导出数据库实例
export default codeCenterDB;
