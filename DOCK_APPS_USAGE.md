# DockApps 应用管理功能使用说明

## 功能概述

本功能为 HelloWorld.vue 界面中的 dockApps 数据提供完整的增删改查功能，所有数据都存储在 Dexie 数据库中。

## 主要特性

### 1. 数据存储
- 使用 Dexie 数据库存储应用数据
- 数据库文件：`src/lib/dockAppsDB.js`
- 支持应用的持久化存储

### 2. 应用字段
- **name**: 应用名称（必填）
- **icon**: Lucide 图标名称
- **iconFile**: 用户上传的自定义图标
- **color**: 应用颜色（20种预定义渐变色可选）
- **path**: 应用网址
- **highlight**: 是否高亮显示（默认 true）
- **action**: 应用动作

### 3. 预定义渐变颜色
提供20种不同的渐变颜色供用户选择：
- 紫蓝渐变、橙红渐变、蓝绿渐变、粉红渐变
- 灰蓝渐变、红橙渐变、蓝色渐变、黑绿渐变
- 紫绿渐变、青蓝渐变、金黄渐变、薄荷渐变
- 紫粉渐变、橙黄渐变、蓝紫渐变、绿蓝渐变
- 红紫渐变、黄绿渐变、深蓝渐变、彩虹渐变

## 使用方法

### 1. 添加新应用
1. 点击 dock 中的"添加"按钮
2. 在弹出的表单中填写应用信息：
   - 输入应用名称（必填）
   - 选择图标或上传自定义图标
   - 选择应用颜色
   - 输入应用网址
   - 设置其他选项
3. 点击"添加"按钮保存

### 2. 编辑应用
1. 右键点击要编辑的应用图标
2. 选择"编辑"选项
3. 在弹出的表单中修改应用信息
4. 点击"更新"按钮保存

### 3. 删除应用
1. 右键点击要删除的应用图标
2. 选择"删除"选项
3. 确认删除操作

### 4. 图标上传
- 支持上传自定义图标文件
- 支持常见图片格式（jpg, png, gif 等）
- 上传后会显示预览

## 技术实现

### 数据库操作
```javascript
// 添加应用
await dockAppsDBOperations.addApp(appData);

// 更新应用
await dockAppsDBOperations.updateApp(appId, updates);

// 删除应用
await dockAppsDBOperations.deleteApp(appId);

// 获取所有应用
const apps = await dockAppsDBOperations.getAllApps();

// 搜索应用
const results = await dockAppsDBOperations.searchApps(keyword);
```

### 表单验证
- 应用名称为必填项
- 自动生成唯一 ID
- 防抖处理用户输入

### 右键菜单
- 只对用户自定义应用显示右键菜单
- 系统预设应用不可编辑删除
- 支持键盘和鼠标操作

## 注意事项

1. **系统应用保护**: 预设的系统应用（如思盒OA、ToDoList等）不能被编辑或删除
2. **数据持久化**: 所有应用数据都会自动保存到本地数据库
3. **图标处理**: 自定义图标会转换为 base64 格式存储
4. **响应式设计**: 界面适配不同屏幕尺寸

## 测试

可以在浏览器控制台中运行测试：
```javascript
// 导入测试文件后运行
testDockAppsDB();
```

## 文件结构

```
src/
├── lib/
│   └── dockAppsDB.js          # 数据库操作文件
├── components/
│   └── HelloWorld.vue         # 主界面组件
└── test/
    └── dock-apps-test.js      # 测试文件
```
