<template>
<!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="password-vault-overlay" @click.self="$emit('close')">
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle"></div>
          </div>
          <!-- Password verification screen -->
          <div>

              <div class="dock-app-container">
    <!-- 应用列表 -->
    <div class="app-list">
      <div v-for="app in dockApps" :key="app.id" class="app-item-wrapper">
        <div class="app-item" :style="{ background: app.color }" @click="handleAppClick(app)">
          <div class="app-icon">
            <component :is="app.icon" v-if="typeof app.icon === 'string'" />
            <img v-else :src="app.icon" alt="app icon">
          </div>
          <div class="app-name">{{ app.name }}</div>
        </div>
        <el-button class="app-delete-btn" type="danger" size="small" 
                  @click.stop="deleteApp(app.id)" circle>
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 添加/编辑表单对话框 -->
    <el-dialog :title="currentApp.id ? '编辑应用' : '添加应用'" v-model="dialogVisible" width="50%">
      <el-form :model="currentApp" label-width="100px">
        <el-form-item label="应用名称" required>
          <el-input v-model="currentApp.name" placeholder="请输入应用名称"></el-input>
        </el-form-item>

        <el-form-item label="应用图标">
          <el-upload
            class="icon-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeIconUpload"
            :on-change="handleIconChange">
            <img v-if="currentApp.icon" :src="currentApp.icon" class="app-icon-preview">
            <el-icon v-else class="icon-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="背景颜色" required>
          <el-select v-model="currentApp.color" placeholder="请选择背景颜色">
            <el-option
              v-for="color in colorOptions"
              :key="color.value"
              :label="color.label"
              :value="color.value">
              <div class="color-option" :style="{ background: color.value }"></div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应用路径">
          <el-input v-model="currentApp.path" placeholder="请输入应用URL"></el-input>
        </el-form-item>

        <el-form-item label="高亮显示">
          <el-switch v-model="currentApp.highlight" :active-value="true" :inactive-value="false"></el-switch>
        </el-form-item>

        <el-form-item label="动作函数">
          <el-input v-model="currentApp.action" placeholder="请输入动作函数名"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveApp">保存</el-button>
      </template>
    </el-dialog>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="addNewApp">添加应用</el-button>
      <el-button type="info" @click="loadDefaultApps">加载默认应用</el-button>
    </div>
  </div>

            <button @click="$emit('close')" class="vault-close-btn">
              <X class="vault-icon" />
            </button>
          </div>
          
        </div>
      </Transition>
    </div>
  </Transition>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { dockAppsDB } from '@/lib/dockAppsDB.js'
import { ElMessage, ElMessageBox } from "element-plus";

// 20种渐变颜色选项
const colorOptions = ref([
  { value: 'linear-gradient(135deg, #00d4aa, #00a884)', label: '绿色渐变' },
  { value: 'linear-gradient(135deg, #ff7e5f, #feb47b)', label: '橙色渐变' },
  { value: 'linear-gradient(135deg, #bdc3c7, #2c3e50)', label: '灰色渐变' },
  { value: 'linear-gradient(135deg, #ff9a9e, #fad0c4)', label: '粉色渐变' },
  { value: 'linear-gradient(135deg, #667eea, #764ba2)', label: '紫色渐变' },
  { value: 'linear-gradient(135deg, #ff6b6b, #ee5a52)', label: '红色渐变' },
  { value: 'linear-gradient(135deg, #55a3ff, #2e86de)', label: '蓝色渐变' },
  { value: 'linear-gradient(135deg, #000000, #434343, #00ff9d)', label: '黑绿渐变' },
  { value: 'linear-gradient(135deg, #d8b5ff, #1eae98)', label: '紫绿渐变' },
  { value: 'linear-gradient(135deg, #00b894, #00cec9)', label: '蓝绿渐变' },
  { value: 'linear-gradient(135deg, #f093fb, #f5576c)', label: '粉红渐变' },
  { value: 'linear-gradient(135deg, #4facfe, #00f2fe)', label: '天蓝渐变' },
  { value: 'linear-gradient(135deg, #43e97b, #38f9d7)', label: '青绿渐变' },
  { value: 'linear-gradient(135deg, #fa709a, #fee140)', label: '粉黄渐变' },
  { value: 'linear-gradient(135deg, #30cfd0, #330867)', label: '蓝紫渐变' },
  { value: 'linear-gradient(135deg, #a18cd1, #fbc2eb)', label: '淡紫渐变' },
  { value: 'linear-gradient(135deg, #ffc3a0, #ffafbd)', label: '橙粉渐变' },
  { value: 'linear-gradient(135deg, #96fbc4, #f9f586)', label: '绿黄渐变' },
  { value: 'linear-gradient(135deg, #a1c4fd, #c2e9fb)', label: '淡蓝渐变' },
  { value: 'linear-gradient(135deg, #ff9a9e, #fad0c4)', label: '粉橙渐变' }
])
// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);
const dockApps = ref([])
const dialogVisible = ref(false)
const currentApp = ref({
  id: null,
  name: '',
  icon: '',
  color: 'linear-gradient(135deg, #00d4aa, #00a884)',
  path: '',
  highlight: true,
  action: ''
})

// 加载应用列表
const loadApps = async () => {
  dockApps.value = await dockAppsDB.getAllApps()
}

// 添加新应用
const addNewApp = () => {
  currentApp.value = {
    id: null,
    name: '',
    icon: '',
    color: 'linear-gradient(135deg, #00d4aa, #00a884)',
    path: '',
    highlight: true,
    action: ''
  }
  dialogVisible.value = true
}

// 编辑应用
const editApp = (app) => {
  currentApp.value = { ...app }
  dialogVisible.value = true
}

// 保存应用
const saveApp = async () => {
  if (!currentApp.value.name) {
    ElMessage.error('请填写应用名称')
    return
  }

  try {
    if (currentApp.value.id) {
      await dockAppsDB.updateApp(currentApp.value.id, currentApp.value)
    } else {
      await dockAppsDB.addApp(currentApp.value)
    }
    await loadApps()
    dialogVisible.value = false
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  }
}

// 删除应用
const deleteApp = async (id) => {
  try {
    await dockAppsDB.deleteApp(id)
    await loadApps()
    ElMessage.success('删除成功')
  } catch (error) {
    ElMessage.error('删除失败: ' + error.message)
  }
}

// 处理图标上传
const beforeIconUpload = (file) => {
  const isImage = file.type.includes('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
  }
  return isImage
}

const handleIconChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    currentApp.value.icon = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 处理应用点击
const handleAppClick = (app) => {
  if (app.path) {
    window.open(app.path, '_blank')
  } else if (app.action && typeof window[app.action] === 'function') {
    window[app.action]()
  }
}

// 加载默认应用
const loadDefaultApps = async () => {
  try {
    const defaultApps = [
      {
        name: "思盒OA",
        icon: "",
        color: "linear-gradient(135deg, #00d4aa, #00a884)",
        path: "http://192.168.28.8/hsoftoa/#/login?redirect=%2Fdashboard",
        highlight: true,
      },
      {
        name: "ToDoList",
        icon: "",
        color: "linear-gradient(135deg, #ff7e5f, #feb47b)",
        action: "openToDoList",
        highlight: false,
      },
      // 其他默认应用...
    ]
    
    await db.apps.clear()
    for (const app of defaultApps) {
      await dockAppsDB.addApp(app)
    }
    await loadApps()
    ElMessage.success('默认应用加载成功')
  } catch (error) {
    ElMessage.error('加载默认应用失败: ' + error.message)
  }
}

// 初始化加载
onMounted(() => {
  loadApps()
})
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}
/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  padding: 0 12px;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}
.dock-app-container {
  padding: 20px;
}

.app-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.app-item-wrapper {
  position: relative;
}

.app-item {
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.app-delete-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.app-item-wrapper:hover .app-delete-btn {
  opacity: 1;
}

.app-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.app-icon img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.app-name {
  font-size: 14px;
  font-weight: 500;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.icon-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.icon-uploader:hover {
  border-color: #409eff;
}

.app-icon-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.color-option {
  width: 100%;
  height: 20px;
  border-radius: 4px;
}
</style>
