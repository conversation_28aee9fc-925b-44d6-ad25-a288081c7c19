// DockApps 数据库测试文件
import { dockAppsDBOperations } from '../lib/dockAppsDB.js';

// 测试数据
const testApp = {
  id: 'test-app-' + Date.now(),
  name: '测试应用',
  icon: 'Star',
  iconFile: null,
  color: 'linear-gradient(135deg, #667eea, #764ba2)',
  path: 'https://www.example.com',
  highlight: true,
  action: 'testAction'
};

// 测试函数
async function testDockAppsDB() {
  console.log('开始测试 DockApps 数据库...');

  try {
    // 1. 测试添加应用
    console.log('1. 测试添加应用...');
    const addResult = await dockAppsDBOperations.addApp(testApp);
    console.log('添加结果:', addResult);

    // 2. 测试获取所有应用
    console.log('2. 测试获取所有应用...');
    const allApps = await dockAppsDBOperations.getAllApps();
    console.log('所有应用:', allApps);

    // 3. 测试根据ID获取应用
    console.log('3. 测试根据ID获取应用...');
    const appById = await dockAppsDBOperations.getAppById(testApp.id);
    console.log('根据ID获取的应用:', appById);

    // 4. 测试更新应用
    console.log('4. 测试更新应用...');
    const updateResult = await dockAppsDBOperations.updateApp(testApp.id, {
      name: '更新后的测试应用',
      color: 'linear-gradient(135deg, #ff7e5f, #feb47b)'
    });
    console.log('更新结果:', updateResult);

    // 5. 测试搜索应用
    console.log('5. 测试搜索应用...');
    const searchResult = await dockAppsDBOperations.searchApps('更新');
    console.log('搜索结果:', searchResult);

    // 6. 测试获取应用总数
    console.log('6. 测试获取应用总数...');
    const count = await dockAppsDBOperations.getAppsCount();
    console.log('应用总数:', count);

    // 7. 测试删除应用
    console.log('7. 测试删除应用...');
    const deleteResult = await dockAppsDBOperations.deleteApp(testApp.id);
    console.log('删除结果:', deleteResult);

    // 8. 验证删除后的状态
    console.log('8. 验证删除后的状态...');
    const finalApps = await dockAppsDBOperations.getAllApps();
    console.log('删除后的所有应用:', finalApps);

    console.log('✅ DockApps 数据库测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testDockAppsDB = testDockAppsDB;
  console.log('测试函数已添加到 window.testDockAppsDB，可在控制台中调用');
}

export { testDockAppsDB };
