import <PERSON>ie from 'dexie';

const db = new <PERSON>ie('DockAppsDB');
db.version(1).stores({
  apps: '++id, name, icon, color, path, highlight, action'
});

export const dockAppsDB = {
  async getAllApps() {
    return await db.apps.toArray();
  },
  
  async addApp(app) {
    return await db.apps.add(app);
  },
  
  async updateApp(id, updates) {
    return await db.apps.update(id, updates);
  },
  
  async deleteApp(id) {
    return await db.apps.delete(id);
  },
  
  async getAppById(id) {
    return await db.apps.get(id);
  }
};

export default db;
