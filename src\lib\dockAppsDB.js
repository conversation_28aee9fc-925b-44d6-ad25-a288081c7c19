import Dexie from 'dexie';

/**
 * DockApps数据库类
 * 专门用于管理桌面应用图标的数据存储
 */
class DockAppsDB extends Dexie {
  constructor() {
    super('DockAppsDB');

    // 定义数据库版本和表结构
    this.version(1).stores({
      apps: '++id, appId, name, icon, iconFile, color, path, highlight, action, createdAt, updatedAt'
    });

    this.apps = this.table('apps');
  }
}

// 创建数据库实例
const dockAppsDB = new DockAppsDB();

/**
 * DockApps数据库操作方法
 */
export const dockAppsDBOperations = {

  /**
   * 添加新应用到数据库
   * @param {Object} app - 应用对象
   * @param {string} app.appId - 应用的唯一标识符
   * @param {string} app.name - 应用名称
   * @param {string} app.icon - 图标名称（Lucide图标）
   * @param {string} app.iconFile - 用户上传的图标文件路径
   * @param {string} app.color - 应用颜色（渐变色）
   * @param {string} app.path - 应用路径/网址
   * @param {boolean} app.highlight - 是否高亮显示
   * @param {string} app.action - 应用动作
   * @returns {Promise<number>} 返回新增记录的ID
   */
  async addApp(app) {
    try {
      const cleanApp = {
        appId: app.id || this.generateId(), // 使用原始的appId作为唯一标识
        name: app.name || '',
        icon: app.icon || 'Plus',
        iconFile: app.iconFile || null,
        color: app.color || 'linear-gradient(135deg, #667eea, #764ba2)',
        path: app.path || '',
        highlight: app.highlight !== undefined ? app.highlight : true,
        action: app.action || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await dockAppsDB.apps.add(cleanApp);
      console.log('应用已保存到数据库:', cleanApp);
      return result;
    } catch (error) {
      console.error('添加应用到数据库失败:', error);
      throw error;
    }
  },

  /**
   * 更新应用数据
   * @param {string} appId - 应用的唯一标识符
   * @param {Object} updates - 要更新的字段
   * @returns {Promise<number>} 返回更新的记录数
   */
  async updateApp(appId, updates) {
    try {
      const cleanUpdates = {
        ...updates,
        updatedAt: new Date()
      };

      // 根据appId查找并更新
      const result = await dockAppsDB.apps
        .where('appId')
        .equals(appId)
        .modify(cleanUpdates);

      console.log('应用已更新:', { appId, updates: cleanUpdates });
      return result;
    } catch (error) {
      console.error('更新应用失败:', error);
      throw error;
    }
  },

  /**
   * 删除应用
   * @param {string} appId - 应用的唯一标识符
   * @returns {Promise<number>} 返回删除的记录数
   */
  async deleteApp(appId) {
    try {
      const result = await dockAppsDB.apps
        .where('appId')
        .equals(appId)
        .delete();

      console.log('应用已从数据库删除:', appId);
      return result;
    } catch (error) {
      console.error('删除应用失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有应用
   * @returns {Promise<Array>} 返回所有应用数组
   */
  async getAllApps() {
    try {
      const apps = await dockAppsDB.apps
        .orderBy('createdAt')
        .toArray();

      console.log('从数据库获取所有应用:', apps);
      return apps;
    } catch (error) {
      console.error('获取所有应用失败:', error);
      throw error;
    }
  },

  /**
   * 根据appId获取应用
   * @param {string} appId - 应用的唯一标识符
   * @returns {Promise<Object|undefined>} 返回应用对象或undefined
   */
  async getAppById(appId) {
    try {
      const app = await dockAppsDB.apps
        .where('appId')
        .equals(appId)
        .first();

      return app;
    } catch (error) {
      console.error('根据ID获取应用失败:', error);
      throw error;
    }
  },

  /**
   * 搜索应用（根据名称）
   * @param {string} keyword - 搜索关键词
   * @returns {Promise<Array>} 返回匹配的应用数组
   */
  async searchApps(keyword) {
    try {
      if (!keyword || keyword.trim() === '') {
        return await this.getAllApps();
      }

      const apps = await dockAppsDB.apps
        .filter(app =>
          app.name.toLowerCase().includes(keyword.toLowerCase())
        )
        .toArray();

      console.log('应用搜索结果:', apps);
      return apps;
    } catch (error) {
      console.error('搜索应用失败:', error);
      throw error;
    }
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  },

  /**
   * 清空所有应用（用于测试或重置）
   * @returns {Promise<void>}
   */
  async clearAllApps() {
    try {
      await dockAppsDB.apps.clear();
      console.log('所有应用已清空');
    } catch (error) {
      console.error('清空应用失败:', error);
      throw error;
    }
  },

  /**
   * 获取应用总数
   * @returns {Promise<number>} 返回应用总数
   */
  async getAppsCount() {
    try {
      const count = await dockAppsDB.apps.count();
      return count;
    } catch (error) {
      console.error('获取应用总数失败:', error);
      throw error;
    }
  }
};

// 默认导出数据库实例
export default dockAppsDB;
