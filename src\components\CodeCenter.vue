<template>
  <!-- Component Manager Vault Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="password-vault-overlay" @click.self="$emit('close')">
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles background effect -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle"></div>
          </div>
          
          <!-- Header with close button -->
          <div class="vault-header">
            <h2 class="vault-title">代码管理中心CodeCenter</h2>
            <p class="vault-subtitle">前端组件代码管理CC</p>
            <button @click="$emit('close')" class="vault-close-btn">
              <X class="vault-icon" />
            </button>
          </div>
          
          <!-- Main content tabs -->
          <div class="vault-tabs">
            <button 
              :class="['tab-btn', activeTab === 'upload' ? 'tab-active' : '']" 
              @click="activeTab = 'upload'"
            >
              <Upload class="tab-icon" />
              <span>上传组件</span>
            </button>
            <button 
              :class="['tab-btn', activeTab === 'library' ? 'tab-active' : '']" 
              @click="activeTab = 'library'"
            >
              <BookOpen class="tab-icon" />
              <span>组件库</span>
            </button>
          </div>
          
          <!-- Upload form tab -->
          <div v-if="activeTab === 'upload'" class="tab-content upload-form">
            <div class="form-grid">
              <!-- Component Info -->
              <div class="form-section">
                <h3 class="section-title">组件信息</h3>
                
                <div class="form-group">
                  <label for="componentName">组件名称 *</label>
                  <input 
                    type="text" 
                    id="componentName" 
                    v-model="newComponent.name"
                    placeholder="公共弹框、table、echarts地图..."
                    class="form-input"
                  >
                </div>
                
                <div class="form-group">
                  <label for="componentDescription">描述</label>
                  <textarea 
                    id="componentDescription" 
                    v-model="newComponent.description"
                    placeholder="描述组件的使用场景"
                    class="form-textarea"
                  ></textarea>
                </div>
                
                <div class="form-group">
                  <label for="componentCategory">分类</label>



                  <el-select id="componentCategory" v-model="newComponent.category" class="form-select" placeholder="请选择">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>

                  <!-- <select 
                    id="componentCategory" 
                    v-model="newComponent.category"
                    class="form-select"
                  >
                    <option value="">选择类别</option>
                    <option value="ui">UI Components</option>
                    <option value="layout">Layout</option>
                    <option value="form">Form Elements</option>
                    <option value="data">Data Display</option>
                    <option value="other">Other</option>
                  </select> -->
                </div>
                
                <div class="form-group">
                  <label for="componentUsage">用途描述</label>
                  <textarea 
                    id="componentUsage" 
                    v-model="newComponent.usage"
                    placeholder="组件的用途描述"
                    class="form-textarea"
                  ></textarea>
                </div>
              </div>
              
              <!-- File Uploads -->
              <div class="form-section">
                <h3 class="section-title">文件上传</h3>
                
                <!-- Code file upload -->
                <div class="upload-group">
                  <label>组件代码 *</label>
                  <div 
                    class="file-drop-area" 
                    @dragover.prevent 
                    @dragleave.prevent 
                    @drop.prevent="handleCodeFileDrop"
                    :class="{ 'file-drop-active': isCodeDropActive }"
                  >
                    <Code2 class="upload-icon" />
                    <p>拖拽/上传代码文件</p>
                    <p class="file-format-info">支持: .vue, .html, .js, .jsx, .ts, .tsx, .css, .scss</p>
                    <input 
                      type="file" 
                      ref="codeFileInput" 
                      @change="handleCodeFileSelect"
                      accept=".vue,.html,.js,.jsx,.ts,.tsx,.css,.scss"
                      class="file-input"
                    >
                    
                    <div v-if="newComponent.codeFile || (isEditMode && newComponent.codeContent)" class="file-preview">
                      <div class="file-info">
                        <FileText class="file-icon" />
                        <div>
                          <p class="file-name">{{ newComponent.codeFile?.name || (isEditMode ? '已上传的代码文件' : '') }}</p>
                          <p class="file-size">{{ newComponent.codeFile ? formatFileSize(newComponent.codeFile.size) : (isEditMode && newComponent.codeContent ? `${Math.round(newComponent.codeContent.length / 1024)} KB` : '') }}</p>
                        </div>
                      </div>
                      <button @click="clearCodeFile" class="remove-file-btn">
                        <Trash2 class="trash-icon" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <!-- Preview image upload -->
                <div class="upload-group">
                  <label>展示效果</label>
                  <div 
                    class="file-drop-area" 
                    @dragover.prevent 
                    @dragleave.prevent 
                    @drop.prevent="handleImageFileDrop"
                    :class="{ 'file-drop-active': isImageDropActive }"
                  >
                    <ImageIcon class="upload-icon" />
                    <p>拖拽/上传展示图片</p>
                    <p class="file-format-info">支持: .png, .jpg, .jpeg, .gif, .webp</p>
                    <input 
                      type="file" 
                      ref="imageFileInput" 
                      @change="handleImageFileSelect"
                      accept="image/*"
                      class="file-input"
                    >
                    
                    <div v-if="newComponent.imageUrl" class="image-preview">
                      <img :src="newComponent.imageUrl" :alt="newComponent.name + ' preview'">
                      <button @click="clearImageFile" class="remove-image-btn">
                        <Trash2 class="trash-icon" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <button
                  class="submit-btn"
                  @click="saveComponent"
                  :disabled="!isComponentValid"
                >
                  <Save class="submit-icon" />
                  {{ isEditMode ? '更新组件' : '保存组件' }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- Component library tab -->
          <div v-if="activeTab === 'library'" class="tab-content library-content">
            <!-- Search and filters -->
            <div class="library-controls">
              <div class="search-bar">
                <Search class="search-icon" />
                <input 
                  type="text" 
                  v-model="searchQuery"
                  placeholder="搜索组件"
                  class="search-input"
                >
              </div>

              <el-select class="category-filter" v-model="filterCategory" placeholder="请选择" @change="filterComponents">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              
              <!-- <select 
                v-model="filterCategory"
                class="category-filter"
                @change="filterComponents"
              >
                <option value="">所有分类</option>
                <option value="ui">UI Components</option>
                <option value="layout">Layout</option>
                <option value="form">Form Elements</option>
                <option value="data">Data Display</option>
                <option value="other">Other</option>
              </select> -->
            </div>
            
            <!-- Empty state -->
            <div v-if="filteredComponents.length === 0" class="empty-state">
              <Box class="empty-icon" />
              <h3>没发现组件</h3>
              <p>请上传第一个组件</p>
              <button 
                class="empty-upload-btn"
                @click="activeTab = 'upload'"
              >
                <Upload class="btn-icon" />
                上传组件
              </button>
            </div>
            
            <!-- Components grid -->
            <div v-else class="components-grid">
              <div 
                v-for="(component, index) in filteredComponents" 
                :key="index"
                class="component-card"
                @click="viewComponentDetails(component)"
              >
                <div class="component-preview">
                  <img 
                    v-if="component.imageUrl" 
                    :src="component.imageUrl" 
                    :alt="component.name + ' preview'"
                    class="preview-img"
                  >
                  <div v-else class="no-preview">
                    <Code2 class="no-preview-icon" />
                  </div>
                  <div class="component-category">
                    {{ component.category || 'Uncategorized' }}
                  </div>
                </div>
                <div class="component-info">
                  <h3 class="component-name">{{ component.name }}</h3>
                  <p class="component-desc">{{ truncateText(component.description, 80) }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Component details modal -->
          <Transition name="detail-modal">
            <div v-if="showComponentDetail" class="detail-overlay" @click.self="closeComponentDetail">
              <div class="detail-container">
                <div class="detail-header">
                  <h2 class="detail-title">{{ selectedComponent.name }}</h2>
                  <div class="detail-actions">
                    <button 
                      class="edit-btn"
                      @click="editComponent"
                    >
                      <Edit3 class="action-icon" />
                    </button>
                    <button 
                      class="delete-btn"
                      @click="deleteComponent"
                    >
                      <Trash2 class="action-icon" />
                    </button>
                    <button 
                      class="close-detail-btn"
                      @click="closeComponentDetail"
                    >
                      <X class="action-icon" />
                    </button>
                  </div>
                </div>
                
                <div class="detail-content">
                  <div class="detail-section">
                    <h3 class="detail-section-title">Preview</h3>
                    <div class="detail-preview">
                      <img 
                        v-if="selectedComponent.imageUrl" 
                        :src="selectedComponent.imageUrl" 
                        :alt="selectedComponent.name + ' preview'"
                        class="detail-preview-img"
                      >
                      <div v-else class="no-detail-preview">
                        <ImageOff class="no-image-icon" />
                        <p>No preview image available</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="detail-section">
                    <h3 class="detail-section-title">Details</h3>
                    <div class="detail-info-grid">
                      <div class="info-item">
                        <span class="info-label">Category</span>
                        <span class="info-value">{{ selectedComponent.category || 'Uncategorized' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">File</span>
                        <span class="info-value">{{ selectedComponent.codeFile?.name || 'Unknown' }}</span>
                      </div>
                      <div class="info-item full-width">
                        <span class="info-label">Description</span>
                        <p class="info-description">{{ selectedComponent.description || 'No description provided' }}</p>
                      </div>
                      <div class="info-item full-width">
                        <span class="info-label">Usage Scenarios</span>
                        <p class="info-description">{{ selectedComponent.usage || 'No usage information provided' }}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="detail-section">
                    <h3 class="detail-section-title">Code</h3>
                    <div class="code-header">
                      <div class="code-language">
                        {{ getFileExtension(selectedComponent.codeFile?.name) || 'Code' }}
                      </div>
                      <button 
                        class="copy-code-btn"
                        @click="copyToClipboard"
                        :class="{ 'copied': codeCopied }"
                      >
                        <Clipboard class="copy-icon" />
                        <span>{{ codeCopied ? 'Copied!' : 'Copy Code' }}</span>
                      </button>
                    </div>
                    <pre class="code-block"><code :class="`language-${getFileExtension(selectedComponent.codeFile?.name)}`" v-html="formattedCode"></code></pre>
                  </div>
                </div>
              </div>
            </div>
          </Transition>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { componentDB } from '@/lib/codeCenterDB.js';
import { 
  X, Upload, BookOpen, Code2, FileText, Trash2, ImageIcon, 
  Search, Box, Save, Edit3, Clipboard, ImageOff
} from 'lucide-vue-next';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);

// State
const activeTab = ref('library');
const components = ref([]);
const filteredComponents = ref([]);
const searchQuery = ref('');
const filterCategory = ref('');
const showComponentDetail = ref(false);
const selectedComponent = ref(null);
const codeCopied = ref(false);
const formattedCode = ref('');
const isEditMode = ref(false);
const originalComponentIndex = ref(-1);

// New component state
const newComponent = ref({
  id: null, // 添加ID字段
  name: '',
  description: '',
  category: '',
  usage: '',
  codeFile: null,
  codeContent: '',
  imageFile: null,
  imageUrl: ''
});
const options = ref([
  {
    value: '',
    label: '所有类型'
  },
  {
    value: 'ui',
    label: '图表类'
  },
  {
    value: 'layout',
    label: '通用类'
  },
  {
    value: 'form',
    label: '功能型'
  },
  {
    value: 'data',
    label: '数据展示'
  },
  {
    value: 'other',
    label: '其他'
  }
]);
// File drop states
const isCodeDropActive = ref(false);
const isImageDropActive = ref(false);

// File input refs
const codeFileInput = ref(null);
const imageFileInput = ref(null);

// Initialize with saved components from database
onMounted(async () => {
  const dbComponents = await componentDB.getAllComponents();
  components.value = dbComponents;
  filteredComponents.value = [...components.value];
});

const filterComponents = () => {
  let results = [...components.value];
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    results = results.filter(component => 
      component.name.toLowerCase().includes(query) ||
      component.description.toLowerCase().includes(query) ||
      component.usage.toLowerCase().includes(query)
    );
  }
  
  // Filter by category
  if (filterCategory.value) {
    results = results.filter(component => 
      component.category === filterCategory.value
    );
  }
  
  filteredComponents.value = results;
};

// Watch for search query changes
watch(searchQuery, filterComponents);

// Computed properties
const isComponentValid = computed(() => {
  return newComponent.value.name.trim() !== '' &&
         newComponent.value.codeContent !== '' &&
         (newComponent.value.codeFile !== null || isEditMode.value); // 编辑模式下不需要新文件
});

// File handling methods
const handleCodeFileSelect = (e) => {
  const file = e.target.files[0];
  if (file) {
    processCodeFile(file);
  }
  // Reset input to allow re-selection of same file
  e.target.value = '';
};

const handleCodeFileDrop = (e) => {
  isCodeDropActive.value = false;
  const file = e.dataTransfer.files[0];
  if (file) {
    processCodeFile(file);
  }
};

const processCodeFile = (file) => {
  newComponent.value.codeFile = file;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    newComponent.value.codeContent = e.target.result;
  };
  reader.readAsText(file);
};

const handleImageFileSelect = (e) => {
  const file = e.target.files[0];
  if (file) {
    processImageFile(file);
  }
  // Reset input to allow re-selection of same file
  e.target.value = '';
};

const handleImageFileDrop = (e) => {
  isImageDropActive.value = false;
  const file = e.dataTransfer.files[0];
  if (file) {
    processImageFile(file);
  }
};

const processImageFile = (file) => {
  newComponent.value.imageFile = file;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    newComponent.value.imageUrl = e.target.result;
  };
  reader.readAsDataURL(file);
};

const clearCodeFile = () => {
  newComponent.value.codeFile = null;
  newComponent.value.codeContent = '';
};

const clearImageFile = () => {
  newComponent.value.imageFile = null;
  newComponent.value.imageUrl = '';
};

// Component management methods
const saveComponent = async () => {
  if (!isComponentValid.value) return;

  try {
    if (isEditMode.value && newComponent.value.id) {
      // 编辑模式：更新现有组件
      const componentToUpdate = {
        name: newComponent.value.name,
        description: newComponent.value.description,
        category: newComponent.value.category,
        usage: newComponent.value.usage,
        codeContent: newComponent.value.codeContent,
        imageUrl: newComponent.value.imageUrl
      };

      await componentDB.updateComponent(newComponent.value.id, componentToUpdate);
      isEditMode.value = false;
      originalComponentIndex.value = -1;
    } else {
      // 新增模式：添加新组件（不需要手动设置ID，让数据库自动生成）
      const componentToAdd = {
        name: newComponent.value.name,
        description: newComponent.value.description,
        category: newComponent.value.category,
        usage: newComponent.value.usage,
        codeContent: newComponent.value.codeContent,
        imageUrl: newComponent.value.imageUrl
      };

      await componentDB.addComponent(componentToAdd);
    }

    // Refresh components from database
    const dbComponents = await componentDB.getAllComponents();
    components.value = dbComponents;
    filteredComponents.value = [...components.value];

    // Reset form
    resetForm();

    // Show success animation or message could go here
    activeTab.value = 'library';
  } catch (error) {
    console.error('保存组件时出错:', error);
    alert('保存失败，请重试');
  }
};

const resetForm = () => {
  newComponent.value = {
    id: null, // 重置时清空ID
    name: '',
    description: '',
    category: '',
    usage: '',
    codeFile: null,
    codeContent: '',
    imageFile: null,
    imageUrl: ''
  };
};



const viewComponentDetails = (component) => {
  selectedComponent.value = { ...component };
  formatComponentCode();
  showComponentDetail.value = true;
};

const formatComponentCode = () => {
  if (!selectedComponent.value?.codeContent) {
    formattedCode.value = '';
    return;
  }
  
  const fileExt = getFileExtension(selectedComponent.value.codeFile?.name);
  // 确保语言子集是有效的数组，如果没有有效的文件扩展名则不传递
  const languageSubset = fileExt ? [fileExt] : undefined;
  
  // 使用正确的参数调用 highlightAuto
  const result = languageSubset 
    ? hljs.highlightAuto(selectedComponent.value.codeContent, languageSubset)
    : hljs.highlightAuto(selectedComponent.value.codeContent);
  
  formattedCode.value = result.value;
};

const closeComponentDetail = () => {
  showComponentDetail.value = false;
  selectedComponent.value = null;
  codeCopied.value = false;
};

const editComponent = () => {
  if (selectedComponent.value) {
    // Find the index of the component to edit
    originalComponentIndex.value = components.value.findIndex(
      comp => comp.id === selectedComponent.value.id
    );

    // Populate form with component data, including the original ID
    newComponent.value = {
      id: selectedComponent.value.id, // 保持原有ID
      name: selectedComponent.value.name,
      description: selectedComponent.value.description,
      category: selectedComponent.value.category,
      usage: selectedComponent.value.usage,
      codeFile: selectedComponent.value.codeFile,
      codeContent: selectedComponent.value.codeContent,
      imageFile: selectedComponent.value.imageFile,
      imageUrl: selectedComponent.value.imageUrl
    };

    isEditMode.value = true;
    closeComponentDetail();
    activeTab.value = 'upload';

    // Scroll to top of upload form
    setTimeout(() => {
      document.querySelector('.upload-form').scrollTop = 0;
    }, 100);
  }
};

const deleteComponent = async () => {
  if (selectedComponent.value && confirm('Are you sure you want to delete this component?')) {
    await componentDB.deleteComponent(selectedComponent.value.id);
    
    // Refresh components from database
    const dbComponents = await componentDB.getAllComponents();
    components.value = dbComponents;
    filteredComponents.value = [...components.value];
    
    closeComponentDetail();
  }
};

const copyToClipboard = () => {
  if (selectedComponent.value?.codeContent) {
    navigator.clipboard.writeText(selectedComponent.value.codeContent);
    codeCopied.value = true;
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      codeCopied.value = false;
    }, 2000);
  }
};

// Helper methods
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const getFileExtension = (filename) => {
  if (!filename) return ''; // 返回空字符串而不是 undefined
  const ext = filename.split('.').pop().toLowerCase();
  // 映射一些可能的扩展到 highlight.js 支持的语言名
  const extMap = {
    'jsx': 'javascript',
    'tsx': 'typescript',
    'vue': 'vue',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'js': 'javascript',
    'ts': 'typescript'
  };
  return extMap[ext] || ext;
};
</script>

<style scoped>

button:focus {
  outline: none;
}
/* Animations and transitions */
@keyframes vault-bounce-in {
  0% { transform: scale(0.8); opacity: 0; }
  70% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); }
}

@keyframes vault-bounce-out {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.detail-modal-enter-active, .detail-modal-leave-active {
  transition: all 0.3s ease;
}

.detail-modal-enter-from, .detail-modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Modal base styles */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 20px 0;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  min-height: 600px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* Background particles effect */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.vault-particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: float 8s infinite ease-in-out;
}

.vault-particle:nth-child(odd) {
  background: rgba(102, 16, 242, 0.08);
}

/* Generate random positions and sizes for particles */
.vault-particle:nth-child(1) { top: 10%; left: 20%; width: 30px; height: 30px; animation-delay: 0s; }
.vault-particle:nth-child(2) { top: 80%; left: 40%; width: 20px; height: 20px; animation-delay: 1s; }
.vault-particle:nth-child(3) { top: 30%; left: 70%; width: 25px; height: 25px; animation-delay: 2s; }
.vault-particle:nth-child(4) { top: 60%; left: 10%; width: 15px; height: 15px; animation-delay: 3s; }
.vault-particle:nth-child(5) { top: 20%; left: 90%; width: 40px; height: 40px; animation-delay: 4s; }
.vault-particle:nth-child(6) { top: 50%; left: 50%; width: 35px; height: 35px; animation-delay: 5s; }
.vault-particle:nth-child(7) { top: 90%; left: 80%; width: 20px; height: 20px; animation-delay: 6s; }
.vault-particle:nth-child(8) { top: 10%; left: 5%; width: 25px; height: 25px; animation-delay: 7s; }
.vault-particle:nth-child(9) { top: 70%; left: 60%; width: 30px; height: 30px; animation-delay: 8s; }
.vault-particle:nth-child(10) { top: 40%; left: 30%; width: 15px; height: 15px; animation-delay: 9s; }
.vault-particle:nth-child(11) { top: 25%; left: 50%; width: 20px; height: 20px; animation-delay: 0.5s; }
.vault-particle:nth-child(12) { top: 65%; left: 25%; width: 35px; height: 35px; animation-delay: 1.5s; }
.vault-particle:nth-child(13) { top: 5%; left: 60%; width: 20px; height: 20px; animation-delay: 2.5s; }
.vault-particle:nth-child(14) { top: 85%; left: 5%; width: 30px; height: 30px; animation-delay: 3.5s; }
.vault-particle:nth-child(15) { top: 35%; left: 85%; width: 25px; height: 25px; animation-delay: 4.5s; }
.vault-particle:nth-child(16) { top: 55%; left: 95%; width: 15px; height: 15px; animation-delay: 5.5s; }
.vault-particle:nth-child(17) { top: 15%; left: 35%; width: 40px; height: 40px; animation-delay: 6.5s; }
.vault-particle:nth-child(18) { top: 75%; left: 50%; width: 20px; height: 20px; animation-delay: 7.5s; }
.vault-particle:nth-child(19) { top: 45%; left: 15%; width: 25px; height: 25px; animation-delay: 8.5s; }
.vault-particle:nth-child(20) { top: 5%; left: 80%; width: 30px; height: 30px; animation-delay: 9.5s; }

/* Header styles */
.vault-header {
  padding: 30px 40px;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.vault-title {
  color: #ffffff;
  font-size: 28px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.vault-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  margin: 0;
  font-weight: 400;
}

.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  padding: 0 12px;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* Tabs styles */
.vault-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.03);
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 10;
}

.tab-btn {
  flex: 1;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 14px 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}

.tab-active {
  background: rgba(102, 16, 242, 0.2);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(102, 16, 242, 0.15);
}

.tab-icon {
  width: 20px;
  height: 20px;
}

/* Tab content styles */
.tab-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}

/* Upload form styles */
.upload-form {
  padding-bottom: 50px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.form-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.section-title {
  color: #ffffff;
  font-size: 18px;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.form-input, .form-textarea, .form-select {
  width: 100%;
  padding: 12px 15px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 15px;
  transition: all 0.2s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: rgba(102, 16, 242, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 16, 242, 0.1);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* File upload styles */
.upload-group {
  margin-bottom: 25px;
}

.upload-group label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
}

.file-drop-area {
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.file-drop-area:hover {
  border-color: rgba(102, 16, 242, 0.5);
  background: rgba(102, 16, 242, 0.05);
}

.file-drop-active {
  border-color: rgba(102, 16, 242, 0.8);
  background: rgba(102, 16, 242, 0.1);
}

.upload-icon {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 auto 15px;
}

.file-drop-area p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 8px 0;
  font-size: 15px;
}

.upload-link {
  color: #9333ea;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.file-format-info {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.4) !important;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-preview {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  width: 24px;
  height: 24px;
  color: #9333ea;
}

.file-name {
  color: #ffffff;
  margin: 0 0 3px 0;
  font-size: 14px;
  font-weight: 500;
}

.file-size {
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  font-size: 12px;
}

.remove-file-btn {
  background: transparent;
  border: none;
  color: rgba(255, 107, 107, 0.7);
  cursor: pointer;
  padding: 5px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

.trash-icon {
  width: 18px;
  height: 18px;
}

.image-preview {
  margin-top: 20px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-preview img {
  width: 100%;
  display: block;
  object-fit: cover;
  border-radius: 10px;
}

.remove-image-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  /* width: 30px; */
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.remove-image-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.submit-btn {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #6b21a8, #9333ea);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  margin-top: 15px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(147, 51, 234, 0.3);
}

.submit-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-icon {
  width: 18px;
  height: 18px;
}

/* Library tab styles */
.library-content {
  padding-bottom: 50px;
}

.library-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  height: 50px;
}

.search-bar {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.5);
}

.search-input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 15px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(102, 16, 242, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 16, 242, 0.1);
}

.category-filter {
  /* padding: 12px 15px; */
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 15px;
  transition: all 0.2s ease;
  min-width: 200px;
  width: 20%;
}

.category-filter:focus {
  outline: none;
  border-color: rgba(102, 16, 242, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 16, 242, 0.1);
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.component-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.component-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(102, 16, 242, 0.3);
}

.component-preview {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.component-card:hover .preview-img {
  transform: scale(1.05);
}

.no-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
}

.no-preview-icon {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 0.3);
}

.component-category {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 20px;
  backdrop-filter: blur(4px);
}

.component-info {
  padding: 20px;
}

.component-name {
  color: white;
  font-size: 18px;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.component-desc {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  color: rgba(255, 255, 255, 0.2);
}

.empty-state h3 {
  color: white;
  font-size: 20px;
  margin: 0 0 10px 0;
}

.empty-state p {
  margin: 0 0 25px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-upload-btn {
  background: linear-gradient(135deg, #6b21a8, #9333ea);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.empty-upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(147, 51, 234, 0.2);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* Component detail modal */
.detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  z-index: 150;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;
}

.detail-container {
  width: 100%;
  max-width: 1200px;
  background: linear-gradient(135deg, #1a1735, #2a2550);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-title {
  color: white;
  font-size: 24px;
  margin: 0;
}

.detail-actions {
  display: flex;
  gap: 10px;
}

.edit-btn, .delete-btn, .close-detail-btn {
  background: transparent;
  border: none;
  /* width: 40px; */
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
}

.edit-btn:hover {
  background: rgba(96, 165, 250, 0.2);
  transform: translateY(-2px);
}

.delete-btn {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
}

.delete-btn:hover {
  background: rgba(248, 113, 113, 0.2);
  transform: translateY(-2px);
}

.close-detail-btn {
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.05);
}

.close-detail-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-2px);
}

.action-icon {
  width: 20px;
  height: 20px;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

.detail-section {
  margin-bottom: 35px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section-title {
  color: white;
  font-size: 18px;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-preview {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.detail-preview-img {
  width: 100%;
  display: block;
}

.no-detail-preview {
  padding: 60px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.no-image-icon {
  width: 50px;
  height: 50px;
  color: rgba(255, 255, 255, 0.2);
  margin: 0 auto 15px;
}

.detail-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  background: rgba(255, 255, 255, 0.03);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.full-width {
  grid-column: 1 / -1;
}

.info-label {
  display: block;
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  margin-bottom: 5px;
  font-weight: 500;
}

.info-value {
  color: white;
  font-size: 15px;
}

.info-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
  line-height: 1.6;
}

/* Code display styles */
.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.code-language {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  background: rgba(255, 255, 255, 0.05);
  padding: 5px 12px;
  border-radius: 15px;
}

.copy-code-btn {
  background: rgba(102, 16, 242, 0.2);
  color: #c084fc;
  border: none;
  border-radius: 8px;
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.copy-code-btn:hover {
  background: rgba(102, 16, 242, 0.3);
  color: #d8b4fe;
}

.copy-code-btn.copied {
  background: rgba(34, 197, 94, 0.2);
  color: #a7f3d0;
}

.copy-icon {
  width: 16px;
  height: 16px;
}

.code-block {
  background: #1e1e1e;
  border-radius: 12px;
  padding: 20px;
  overflow-x: auto;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 600px) {
  .vault-header {
    padding: 25px 20px;
  }
  
  .vault-title {
    font-size: 24px;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .form-section {
    padding: 20px;
  }
  
  .file-drop-area {
    padding: 20px;
  }
  
  .components-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-container {
    max-height: 95vh;
  }
  
  .detail-header {
    padding: 20px;
  }
  
  .detail-title {
    font-size: 20px;
  }
  
  .detail-content {
    padding: 20px;
  }
}

/* :deep(.el-select) {
  width: 20%;
} */
:deep(.el-select__wrapper) {
  height: 100%;
  border-radius: 10px;
  background-color: transparent;
  box-shadow: none;
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: none;
}
:deep(.el-select__wrapper.is-hovering) {
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
</style>
